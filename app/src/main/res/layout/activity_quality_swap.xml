<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="toolbarVM"
            type="co.styletheory.ops.outbound.android.viewModelComponent.GeneralToolbarViewModel" />

        <variable
            name="footerButtonVM"
            type="co.styletheory.ops.outbound.android.viewModelComponent.FooterButtonViewModel" />

        <variable
            name="viewModel"
            type="co.styletheory.ops.outbound.android.feature.qualitySwap.viewModel.QualitySwapViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/contentContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="false">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <include
                android:id="@+id/toolbar_wrapper"
                layout="@layout/general_toolbar"
                android:viewModel="@{toolbarVM}" />
        </com.google.android.material.appbar.AppBarLayout>

        <View
            android:id="@+id/toolbarDropShadow"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_very_little"
            android:background="@drawable/toolbar_dropshadow"
            app:layout_constraintTop_toBottomOf="@id/appbar" />

        <!-- Footer button that always shows at the bottom of the screen -->
        <FrameLayout
            android:id="@+id/footerContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:elevation="@dimen/space_medium"
            android:fitsSystemWindows="false"
            android:paddingStart="@dimen/space_medium"
            android:paddingTop="@dimen/space_small"
            android:paddingEnd="@dimen/space_medium"
            android:paddingBottom="@dimen/space_small"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <include
                android:id="@+id/swapNowButton"
                layout="@layout/footer_button_item"
                android:viewModel="@{footerButtonVM}" />

        </FrameLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/no_space"
            android:background="@color/white"
            android:fitsSystemWindows="false"
            app:layout_constraintBottom_toTopOf="@id/footerContainer"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/toolbarDropShadow">

            <androidx.core.widget.NestedScrollView
                android:id="@+id/scrollView"
                android:layout_width="match_parent"
                android:layout_height="@dimen/no_space"
                android:clipToPadding="false"
                android:fillViewport="true"
                android:fitsSystemWindows="false"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:minHeight="?android:attr/actionBarSize"
                    android:paddingBottom="@dimen/space_medium_large">

                    <LinearLayout
                        android:id="@+id/layoutInfo"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/space_medium"
                        android:background="@drawable/rect_light_gray_rad_4"
                        android:orientation="horizontal"
                        android:padding="@dimen/space_small"
                        android:visibility="@{viewModel.infoVisibility}"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginEnd="@dimen/space_small"
                            android:contentDescription="@string/quality_swap_title"
                            android:src="@drawable/ic_info" />

                        <TextView
                            style="@style/Widget.TextView.SubHeader2"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="@string/quality_swap_info" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/failReasonTitle"
                        style="@style/Widget.TextView.SubHeader.Bold"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/space_medium"
                        android:layout_marginTop="@dimen/space_size_40"
                        android:layout_marginEnd="@dimen/space_medium"
                        android:layout_marginBottom="@dimen/space_medium"
                        android:text="@string/quality_swap_reason_title"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/layoutInfo" />

                    <Spinner
                        android:id="@+id/swapReasonSpinner"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/space_medium"
                        android:background="@drawable/spinner_background"
                        android:dropDownVerticalOffset="@dimen/space_size_50"
                        android:popupBackground="@color/white"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/failReasonTitle" />

                    <TextView
                        android:id="@+id/notesTitle"
                        style="@style/Widget.TextView.SubHeader.Bold"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/space_medium"
                        android:layout_marginTop="@dimen/space_size_40"
                        android:layout_marginEnd="@dimen/space_medium"
                        android:layout_marginBottom="@dimen/space_medium"
                        android:text="@string/quality_notes_title"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/swapReasonSpinner" />

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/notesContent"
                        style="@style/Widget.TextView.SubHeader2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/space_medium"
                        android:background="@drawable/rect_gray26_rad_4"
                        android:gravity="top"
                        android:hint="@string/quality_notes_placeholder"
                        android:inputType="textMultiLine"
                        android:minHeight="@dimen/space_size_100"
                        android:padding="@dimen/space_semi_medium"
                        android:textColor="@color/very_dark_gray"
                        android:textColorHint="@color/very_dark_gray"
                        app:editTextBinding="@{viewModel.inputNotes}"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/notesTitle" />

                    <TextView
                        android:id="@+id/photosTitle"
                        style="@style/Widget.TextView.SubHeader.Bold"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/space_medium"
                        android:layout_marginTop="@dimen/space_size_40"
                        android:layout_marginEnd="@dimen/space_medium"
                        android:layout_marginBottom="@dimen/space_medium"
                        android:text="@string/quality_photos_title"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/notesContent" />

                    <Button
                        style="@style/Widget.Button.Small.SemiBold"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/space_size_45"
                        android:layout_margin="@dimen/space_medium"
                        android:layout_marginTop="@dimen/space_small"
                        android:background="@drawable/selector_button_outline_green_rad_5"
                        android:foreground="?attr/selectableItemBackground"
                        android:onClick="@{() -> viewModel.uploadPhotoClick()}"
                        android:paddingStart="@dimen/space_semi_medium"
                        android:paddingTop="@dimen/space_medium"
                        android:paddingEnd="@dimen/space_semi_medium"
                        android:paddingBottom="@dimen/space_medium"
                        android:text="@string/quality_photos_button_title"
                        android:textAlignment="gravity"
                        android:textColor="@drawable/selector_text_green_white"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/photosTitle" />

                    <!-- Add some bottom padding to ensure content isn't hidden behind the footer -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/space_size_50"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/photosTitle" />

                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.core.widget.NestedScrollView>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>